<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3D Object Builder</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      overflow: hidden;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .canvas-container {
      flex: 1;
      position: relative;
      min-height: 60vh;
    }

    .sidebar {
      height: 40vh;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      padding: 20px;
      overflow-y: auto;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      gap: 20px;
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 24px;
      text-align: center;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section {
      margin-bottom: 15px;
      padding: 15px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      flex: 1;
      min-width: 200px;
    }

    .section h3 {
      margin: 0 0 15px 0;
      color: #4ecdc4;
      font-size: 16px;
    }

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 10px;
    }

    input[type="number"] {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      font-size: 14px;
      box-sizing: border-box;
    }

    button {
      padding: 10px 15px;
      border: none;
      border-radius: 8px;
      background: linear-gradient(45deg, #ff6b6b, #ee5a6f);
      color: white;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s ease;
      width: 100%;
      margin-bottom: 10px;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    }

    button:disabled {
      background: #666;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .vertex-list {
      max-height: 100px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 5px;
      padding: 10px;
    }

    .edge-list {
      max-height: 80px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 10px;
    }

    .vertex-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px;
      margin: 2px 0;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      font-size: 12px;
    }

    .edge-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3px 5px;
      margin: 2px 0;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      font-size: 12px;
    }

    .edge-item.selected {
      background: rgba(255, 255, 0, 0.3);
      border: 1px solid #ffff00;
    }

    .delete-btn {
      background: #ff4757;
      color: white;
      border: none;
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      cursor: pointer;
      margin: 0;
      width: auto;
    }

    .delete-btn:hover {
      background: #ff3838;
      transform: none;
      box-shadow: none;
    }

    .vertex-item.selected {
      background: rgba(76, 201, 196, 0.3);
      border: 1px solid #4ecdc4;
    }

    .vertex-item:hover {
      background: rgba(255, 255, 255, 0.2);
      cursor: pointer;
    }

    .edge-controls {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .controls {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      padding: 15px;
      border-radius: 10px;
      backdrop-filter: blur(10px);
    }

    .mode-indicator {
      text-align: center;
      padding: 8px;
      background: rgba(76, 201, 196, 0.2);
      border-radius: 5px;
      margin-bottom: 15px;
      border: 1px solid #4ecdc4;
    }

    select {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      margin-bottom: 10px;
    }
