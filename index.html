<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>3D Object Builder</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      overflow: hidden;
    }

    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .canvas-container {
      flex: 1;
      position: relative;
      min-height: 60vh;
    }

    .sidebar {
      height: 40vh;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      padding: 20px;
      overflow-y: auto;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      gap: 20px;
    }

    h1 {
      margin: 0 0 20px 0;
      font-size: 24px;
      text-align: center;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .section {
      margin-bottom: 15px;
      padding: 15px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      flex: 1;
      min-width: 200px;
    }

    .section h3 {
      margin: 0 0 15px 0;
      color: #4ecdc4;
      font-size: 16px;
    }

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 10px;
    }

    input[type="number"] {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      font-size: 14px;
      box-sizing: border-box;
    }

    button {
      padding: 10px 15px;
      border: none;
      border-radius: 8px;
      background: linear-gradient(45deg, #ff6b6b, #ee5a6f);
      color: white;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s ease;
      width: 100%;
      margin-bottom: 10px;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    }

    button:disabled {
      background: #666;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .vertex-list {
      max-height: 100px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 5px;
      padding: 10px;
    }

    .edge-list {
      max-height: 80px;
      overflow-y: auto;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 10px;
    }

    .edge-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3px 5px;
      margin: 2px 0;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      font-size: 12px;
    }

    .edge-item.selected {
      background: rgba(255, 255, 0, 0.3);
      border: 1px solid #ffff00;
    }

    .vertex-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px;
      margin: 2px 0;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      font-size: 12px;
    }

    .delete-btn {
      background: #ff4757;
      color: white;
      border: none;
      border-radius: 3px;
      padding: 2px 6px;
      font-size: 10px;
      cursor: pointer;
      margin: 0;
      width: auto;
    }

    .delete-btn:hover {
      background: #ff3838;
      transform: none;
      box-shadow: none;
    }

    .vertex-item.selected {
      background: rgba(76, 201, 196, 0.3);
      border: 1px solid #4ecdc4;
    }

    .vertex-item:hover {
      background: rgba(255, 255, 255, 0.2);
      cursor: pointer;
    }

    .edge-controls {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .controls {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      padding: 15px;
      border-radius: 10px;
      backdrop-filter: blur(10px);
    }

    .mode-indicator {
      text-align: center;
      padding: 8px;
      background: rgba(76, 201, 196, 0.2);
      border-radius: 5px;
      margin-bottom: 15px;
      border: 1px solid #4ecdc4;
    }

    select {
      width: 100%;
      padding: 8px;
      border: none;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      margin-bottom: 10px;
    }

    input[type="range"] {
      width: 100%;
      margin: 5px 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      height: 6px;
      outline: none;
      -webkit-appearance: none;
    }

    input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #4ecdc4;
      cursor: pointer;
    }

    input[type="range"]::-moz-range-thumb {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #4ecdc4;
      cursor: pointer;
      border: none;
    }

    input[type="checkbox"] {
      margin-right: 8px;
      transform: scale(1.2);
      accent-color: #4ecdc4;
    }

    label {
      display: flex;
      align-items: center;
      color: white;
      font-size: 14px;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="canvas-container">
    <div class="controls">
      <div>Mouse: Rotate | Wheel: Zoom</div>
    </div>
    <div id="canvas"></div>
  </div>

  <div class="sidebar">
    <div class="section">
      <h3>Add Vertex</h3>
      <div class="mode-indicator" id="modeIndicator">
        Mode: Add Vertices
      </div>
      <div class="input-group">
        <input type="number" id="vertexX" placeholder="X" value="0" step="0.1">
        <input type="number" id="vertexY" placeholder="Y" value="0" step="0.1">
        <input type="number" id="vertexZ" placeholder="Z" value="0" step="0.1">
      </div>
      <button onclick="addVertex()">Add Vertex</button>
    </div>

    <div class="section">
      <h3>Vertices</h3>
      <div class="vertex-list" id="vertexList"></div>
      <div>Selected: <span id="selectedCount">0</span> vertices</div>
      <button onclick="clearSelection()">Clear Selection</button>
    </div>

    <div class="section">
      <h3>Edges</h3>
      <div class="edge-list" id="edgeList"></div>
      <button onclick="connectVertices()" id="connectBtn" disabled>Connect Selected</button>
    </div>

    <div class="section">
      <h3>Faces</h3>
      <select id="faceSelect">
        <option value="">Select a closed loop...</option>
      </select>
      <button onclick="fillFace()" id="fillBtn" disabled>Fill Face</button>
      <button onclick="toggleFillMode()">Toggle Fill Mode</button>
    </div>

    <div class="section">
      <h3>Grid Settings</h3>
      <div class="input-group">
        <label>
          <input type="checkbox" id="gridVisible" checked onchange="toggleGrid()"> Show Grid
        </label>
      </div>
      <div class="input-group">
        <label>Grid Size: <span id="gridSizeValue">10</span></label>
        <input type="range" id="gridSize" min="5" max="50" value="10" step="5" onchange="updateGridSize()">
      </div>
      <div class="input-group">
        <label>Grid Spacing:</label>
        <input type="number" id="gridSpacing" value="1" min="0.1" max="5" step="0.1" onchange="updateGridSpacing()">
      </div>
      <div class="input-group">
        <label>
          <input type="checkbox" id="show3DGrid" onchange="toggle3DGrid()"> Show 3D Grid Planes
        </label>
      </div>
    </div>

    <div class="section">
      <h3>Actions</h3>
      <button onclick="clearAll()" style="background: linear-gradient(45deg, #ff4757, #ff3838)">Clear All</button>
    </div>
  </div>
</div>

<script>
  let scene, camera, renderer, controls;
  let vertices = [];
  let edges = [];
  let faces = [];
  let selectedVertices = [];
  let fillMode = false;
  let vertexObjects = [];
  let edgeObjects = [];
  let faceObjects = [];
  let selectedEdgeIndex = -1;

  // Grid-related variables
  let gridHelper = null;
  let gridPlanes = [];
  let gridVisible = true;
  let gridSize = 10;
  let gridSpacing = 1;
  let show3DGrid = false;

  // Initialize Three.js scene
  function init() {
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1a1a2e);

    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);

    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth - 300, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    document.getElementById('canvas').appendChild(renderer.domElement);

    // Add lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Initialize grid
    createGrid();

    // Add axes helper
    const axesHelper = new THREE.AxesHelper(5);
    scene.add(axesHelper);

    // Add axis labels with numbers
    addAxisLabels();

    // Simple orbit controls
    setupOrbitControls();

    animate();
  }

  function selectEdge(edgeIndex) {
    // Highlight the selected edge
    edgeObjects.forEach((edge, index) => {
      if (index === edgeIndex) {
        edge.material.color.setHex(0xffff00); // Yellow for selected
      } else {
        edge.material.color.setHex(0xffffff); // White for normal
      }
    });

    // Update edge list to show selection
    updateEdgeList(edgeIndex);
  }

  function addAxisLabels() {
    // Create simple text sprites for axis labels
    const createTextSprite = (text, position, color = 0xffffff, isNumber = false) => {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      canvas.width = 128;
      canvas.height = 64;

      context.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
      context.font = isNumber ? '48px Arial' : '32px Arial';
      context.textAlign = 'center';
      context.fillText(text, 64, 40);

      const texture = new THREE.CanvasTexture(canvas);
      const material = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(material);
      sprite.scale.set(isNumber ? 0.7 : 0.5, isNumber ? 0.35 : 0.25, 1);
      sprite.position.copy(position);

      return sprite;
    };

    // Add X-axis labels (white numbers)
    for (let i = 1; i <= 5; i++) {
      const sprite = createTextSprite(i.toString(), new THREE.Vector3(i, 0, 0), 0xffffff, true);
      scene.add(sprite);

      const negSprite = createTextSprite((-i).toString(), new THREE.Vector3(-i, 0, 0), 0xffffff, true);
      scene.add(negSprite);
    }

    // Add Y-axis labels (white numbers)
    for (let i = 1; i <= 5; i++) {
      const sprite = createTextSprite(i.toString(), new THREE.Vector3(0, i, 0), 0xffffff, true);
      scene.add(sprite);

      const negSprite = createTextSprite((-i).toString(), new THREE.Vector3(0, -i, 0), 0xffffff, true);
      scene.add(negSprite);
    }

    // Add Z-axis labels (white numbers)
    for (let i = 1; i <= 5; i++) {
      const sprite = createTextSprite(i.toString(), new THREE.Vector3(0, 0, i), 0xffffff, true);
      scene.add(sprite);

      const negSprite = createTextSprite((-i).toString(), new THREE.Vector3(0, 0, -i), 0xffffff, true);
      scene.add(negSprite);
    }

    // Add axis name labels at the ends (colored)
    const xLabel = createTextSprite('X', new THREE.Vector3(5.5, 0, 0), 0xff0000);
    const yLabel = createTextSprite('Y', new THREE.Vector3(0, 5.5, 0), 0x00ff00);
    const zLabel = createTextSprite('Z', new THREE.Vector3(0, 0, 5.5), 0x0000ff);

    scene.add(xLabel);
    scene.add(yLabel);
    scene.add(zLabel);
  }

  function setupOrbitControls() {
    let isMouseDown = false;
    let isDragging = false;
    let mouseX = 0;
    let mouseY = 0;
    let targetRotationX = 0;
    let targetRotationY = 0;
    let rotationX = 0;
    let rotationY = 0;

    renderer.domElement.addEventListener('mousedown', (event) => {
      isMouseDown = true;
      isDragging = false;
      mouseX = event.clientX;
      mouseY = event.clientY;
    });

    renderer.domElement.addEventListener('mousemove', (event) => {
      if (isMouseDown) {
        const deltaX = event.clientX - mouseX;
        const deltaY = event.clientY - mouseY;

        // Mark as dragging if mouse moved significantly
        if (Math.abs(deltaX) > 3 || Math.abs(deltaY) > 3) {
          isDragging = true;
        }

        targetRotationY -= deltaX * 0.01; // Non-inverted horizontal
        targetRotationX += deltaY * 0.01; // Inverted vertical

        mouseX = event.clientX;
        mouseY = event.clientY;
      }
    });

    renderer.domElement.addEventListener('mouseup', (event) => {
      isMouseDown = false;

      // Only trigger click if not dragging
      if (!isDragging) {
        handleMouseClick(event);
      }

      isDragging = false;
    });

    renderer.domElement.addEventListener('wheel', (event) => {
      camera.position.multiplyScalar(event.deltaY > 0 ? 1.1 : 0.9);
      event.preventDefault();
    });

    function updateCamera() {
      rotationX += (targetRotationX - rotationX) * 0.1;
      rotationY += (targetRotationY - rotationY) * 0.1;

      const distance = camera.position.length();
      camera.position.x = distance * Math.sin(rotationY) * Math.cos(rotationX);
      camera.position.y = distance * Math.sin(rotationX);
      camera.position.z = distance * Math.cos(rotationY) * Math.cos(rotationX);
      camera.lookAt(0, 0, 0);

      requestAnimationFrame(updateCamera);
    }
    updateCamera();
  }

  function handleMouseClick(event) {
    const raycaster = new THREE.Raycaster();
    const mouse = new THREE.Vector2();

    // Calculate mouse position in normalized device coordinates
    const rect = renderer.domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // Update the picking ray with the camera and mouse position
    raycaster.setFromCamera(mouse, camera);

    // Check for intersections with vertices first (higher priority)
    const vertexIntersects = raycaster.intersectObjects(vertexObjects);
    if (vertexIntersects.length > 0) {
      const clickedVertex = vertexIntersects[0].object;
      const vertexId = clickedVertex.userData.id;
      toggleVertexSelection(vertexId);
      return;
    }

    // Check for intersections with edges
    const edgeIntersects = raycaster.intersectObjects(edgeObjects);
    if (edgeIntersects.length > 0) {
      const clickedEdge = edgeIntersects[0].object;
      const edgeIndex = edgeObjects.indexOf(clickedEdge);
      if (edgeIndex !== -1) {
        selectedEdgeIndex = edgeIndex;
        selectEdge(edgeIndex);
      }
    }
  }

  function animate() {
    requestAnimationFrame(animate);
    renderer.render(scene, camera);
  }

  // Grid management functions
  function createGrid() {
    // Remove existing grid
    if (gridHelper) {
      scene.remove(gridHelper);
    }

    // Remove existing 3D grid planes
    gridPlanes.forEach(plane => scene.remove(plane));
    gridPlanes = [];

    // Create main horizontal grid
    const divisions = Math.floor(gridSize / gridSpacing);
    gridHelper = new THREE.GridHelper(gridSize, divisions, 0x444444, 0x222222);
    gridHelper.visible = gridVisible;
    scene.add(gridHelper);

    // Create 3D grid planes if enabled
    if (show3DGrid) {
      create3DGridPlanes();
    }
  }

  function create3DGridPlanes() {
    const divisions = Math.floor(gridSize / gridSpacing);
    const halfSize = gridSize / 2;

    // XY plane (vertical, facing Z)
    const xyGrid = new THREE.GridHelper(gridSize, divisions, 0x333333, 0x111111);
    xyGrid.rotation.x = Math.PI / 2;
    xyGrid.position.z = 0;
    xyGrid.visible = gridVisible;
    scene.add(xyGrid);
    gridPlanes.push(xyGrid);

    // XZ plane (horizontal, already created as main grid)

    // YZ plane (vertical, facing X)
    const yzGrid = new THREE.GridHelper(gridSize, divisions, 0x333333, 0x111111);
    yzGrid.rotation.z = Math.PI / 2;
    yzGrid.position.x = 0;
    yzGrid.visible = gridVisible;
    scene.add(yzGrid);
    gridPlanes.push(yzGrid);
  }

  function toggleGrid() {
    gridVisible = document.getElementById('gridVisible').checked;

    if (gridHelper) {
      gridHelper.visible = gridVisible;
    }

    gridPlanes.forEach(plane => {
      plane.visible = gridVisible;
    });
  }

  function updateGridSize() {
    gridSize = parseInt(document.getElementById('gridSize').value);
    document.getElementById('gridSizeValue').textContent = gridSize;
    createGrid();
  }

  function updateGridSpacing() {
    gridSpacing = parseFloat(document.getElementById('gridSpacing').value);
    createGrid();
  }

  function toggle3DGrid() {
    show3DGrid = document.getElementById('show3DGrid').checked;
    createGrid();
  }

  function addVertex() {
    const x = parseFloat(document.getElementById('vertexX').value) || 0;
    const y = parseFloat(document.getElementById('vertexY').value) || 0;
    const z = parseFloat(document.getElementById('vertexZ').value) || 0;

    const vertex = { x, y, z, id: vertices.length };
    vertices.push(vertex);

    // Create visual representation
    const geometry = new THREE.SphereGeometry(0.1, 16, 16);
    const material = new THREE.MeshPhongMaterial({ color: 0x4ecdc4 });
    const sphere = new THREE.Mesh(geometry, material);
    sphere.position.set(x, y, z);
    sphere.userData = { type: 'vertex', id: vertex.id };
    scene.add(sphere);
    vertexObjects.push(sphere);

    updateVertexList();

    // Clear inputs
    document.getElementById('vertexX').value = '';
    document.getElementById('vertexY').value = '';
    document.getElementById('vertexZ').value = '';
  }

  function updateVertexList() {
    const list = document.getElementById('vertexList');
    list.innerHTML = '';

    vertices.forEach(vertex => {
      const item = document.createElement('div');
      item.className = 'vertex-item';
      if (selectedVertices.includes(vertex.id)) {
        item.classList.add('selected');
      }
      item.innerHTML = `
                    <span onclick="toggleVertexSelection(${vertex.id})">V${vertex.id}: (${vertex.x.toFixed(1)}, ${vertex.y.toFixed(1)}, ${vertex.z.toFixed(1)})</span>
                    <button class="delete-btn" onclick="deleteVertex(${vertex.id})">×</button>
                `;
      list.appendChild(item);
    });

    updateSelectedCount();
  }

  function toggleVertexSelection(id) {
    const index = selectedVertices.indexOf(id);
    if (index > -1) {
      selectedVertices.splice(index, 1);
      vertexObjects[id].material.color.setHex(0x4ecdc4);
    } else {
      selectedVertices.push(id);
      vertexObjects[id].material.color.setHex(0xff6b6b);
    }
    updateVertexList();
  }

  function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = selectedVertices.length;
    document.getElementById('connectBtn').disabled = selectedVertices.length !== 2;
  }

  function updateEdgeList(highlightIndex = -1) {
    const list = document.getElementById('edgeList');
    list.innerHTML = '';

    edges.forEach((edge, index) => {
      const item = document.createElement('div');
      item.className = 'edge-item';
      if (index === highlightIndex || index === selectedEdgeIndex) {
        item.classList.add('selected');
      }
      const v1 = vertices[edge.v1];
      const v2 = vertices[edge.v2];
      if (v1 && v2) {
        item.innerHTML = `
                        <span onclick="selectEdgeFromList(${index})">E${index}: V${edge.v1} ↔ V${edge.v2}</span>
                        <button class="delete-btn" onclick="deleteEdge(${index})">×</button>
                    `;
        list.appendChild(item);
      }
    });
  }

  function selectEdgeFromList(edgeIndex) {
    selectedEdgeIndex = edgeIndex;
    selectEdge(edgeIndex);
  }

  function connectVertices() {
    if (selectedVertices.length !== 2) return;

    const v1 = vertices[selectedVertices[0]];
    const v2 = vertices[selectedVertices[1]];

    const edge = { v1: selectedVertices[0], v2: selectedVertices[1] };
    edges.push(edge);

    // Create visual representation
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array([
      v1.x, v1.y, v1.z,
      v2.x, v2.y, v2.z
    ]);
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const material = new THREE.LineBasicMaterial({ color: 0xffffff, linewidth: 2 });
    const line = new THREE.Line(geometry, material);
    scene.add(line);
    edgeObjects.push(line);

    clearSelection();
    updateFaceOptions();
    updateEdgeList();
  }

  function clearSelection() {
    selectedVertices.forEach(id => {
      vertexObjects[id].material.color.setHex(0x4ecdc4);
    });
    selectedVertices = [];
    updateVertexList();
  }

  function updateFaceOptions() {
    const select = document.getElementById('faceSelect');
    select.innerHTML = '<option value="">Select a closed loop...</option>';

    // Find closed loops (simplified - just check for triangles and quads for now)
    const loops = findClosedLoops();
    loops.forEach((loop, index) => {
      const option = document.createElement('option');
      option.value = index;
      option.textContent = `Loop ${index + 1} (${loop.length} vertices)`;
      select.appendChild(option);
    });

    document.getElementById('fillBtn').disabled = loops.length === 0;
  }

  function findClosedLoops() {
    if (vertices.length < 3) return [];

    const loops = [];
    const visited = new Set();

    // Try to find closed loops starting from each vertex
    for (let startVertex = 0; startVertex < vertices.length; startVertex++) {
      if (visited.has(startVertex)) continue;

      const loop = findLoopFromVertex(startVertex);
      if (loop && loop.length >= 3) {
        loops.push(loop);
        loop.forEach(vertexId => visited.add(vertexId));
      }
    }

    return loops;
  }

  function findLoopFromVertex(startVertex) {
    const visited = new Set();
    const path = [];

    function dfs(currentVertex, targetVertex, depth) {
      if (depth > 6) return false; // Prevent infinite loops

      if (depth > 2 && currentVertex === targetVertex) {
        return true; // Found a loop back to start
      }

      if (visited.has(currentVertex) && currentVertex !== targetVertex) {
        return false;
      }

      visited.add(currentVertex);
      path.push(currentVertex);

      // Find all connected vertices
      const connectedVertices = [];
      edges.forEach(edge => {
        if (edge.v1 === currentVertex && !visited.has(edge.v2)) {
          connectedVertices.push(edge.v2);
        } else if (edge.v2 === currentVertex && !visited.has(edge.v1)) {
          connectedVertices.push(edge.v1);
        } else if (depth > 2) {
          // Allow returning to start vertex to close the loop
          if (edge.v1 === currentVertex && edge.v2 === targetVertex) {
            connectedVertices.push(edge.v2);
          } else if (edge.v2 === currentVertex && edge.v1 === targetVertex) {
            connectedVertices.push(edge.v1);
          }
        }
      });

      for (const nextVertex of connectedVertices) {
        if (dfs(nextVertex, targetVertex, depth + 1)) {
          return true;
        }
      }

      path.pop();
      if (currentVertex !== targetVertex) {
        visited.delete(currentVertex);
      }
      return false;
    }

    if (dfs(startVertex, startVertex, 0)) {
      // Remove the duplicate start vertex at the end
      if (path[path.length - 1] === startVertex) {
        path.pop();
      }
      return path.length >= 3 ? path : null;
    }

    return null;
  }

  function fillFace() {
    const select = document.getElementById('faceSelect');
    const loopIndex = parseInt(select.value);
    if (isNaN(loopIndex)) return;

    const loops = findClosedLoops();
    const loop = loops[loopIndex];

    if (!loop || loop.length < 3) return;

    // Create face geometry
    const positions = [];
    const indices = [];

    // Get vertex positions
    const loopVertices = loop.map(id => vertices[id]);

    // Simple triangulation for any polygon
    for (let i = 1; i < loopVertices.length - 1; i++) {
      // Add triangle vertices
      positions.push(
        loopVertices[0].x, loopVertices[0].y, loopVertices[0].z,
        loopVertices[i].x, loopVertices[i].y, loopVertices[i].z,
        loopVertices[i + 1].x, loopVertices[i + 1].y, loopVertices[i + 1].z
      );
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(positions), 3));
    geometry.computeVertexNormals();

    const material = new THREE.MeshPhongMaterial({
      color: 0xff6b6b,
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.7
    });
    const mesh = new THREE.Mesh(geometry, material);
    scene.add(mesh);
    faceObjects.push(mesh);

    faces.push({ loop, object: mesh });
  }

  function toggleFillMode() {
    fillMode = !fillMode;
    document.getElementById('modeIndicator').textContent =
      fillMode ? 'Mode: Fill Faces' : 'Mode: Add Vertices';
  }

  function deleteVertex(id) {
    // Remove from selected vertices if selected
    const selectedIndex = selectedVertices.indexOf(id);
    if (selectedIndex > -1) {
      selectedVertices.splice(selectedIndex, 1);
    }

    // Remove connected edges
    const connectedEdges = [];
    edges.forEach((edge, index) => {
      if (edge.v1 === id || edge.v2 === id) {
        connectedEdges.push(index);
        scene.remove(edgeObjects[index]);
      }
    });

    // Remove edges in reverse order to maintain indices
    connectedEdges.reverse().forEach(index => {
      edges.splice(index, 1);
      edgeObjects.splice(index, 1);
    });

    // Remove faces that contain this vertex
    const connectedFaces = [];
    faces.forEach((face, index) => {
      if (face.loop.includes(id)) {
        connectedFaces.push(index);
        scene.remove(face.object);
      }
    });

    // Remove faces in reverse order
    connectedFaces.reverse().forEach(index => {
      faces.splice(index, 1);
      faceObjects.splice(index, 1);
    });

    // Remove vertex object from scene
    scene.remove(vertexObjects[id]);

    // Remove vertex from arrays
    vertices.splice(id, 1);
    vertexObjects.splice(id, 1);

    // Update all vertex IDs and references
    updateVertexIds();

    updateVertexList();
    updateEdgeList();
    updateFaceOptions();
  }

  function updateVertexIds() {
    // Update vertex IDs
    vertices.forEach((vertex, index) => {
      vertex.id = index;
    });

    // Update selected vertices
    selectedVertices = selectedVertices.filter(id => id < vertices.length);

    // Update edge references
    edges.forEach(edge => {
      // Adjust edge references for removed vertices
      if (edge.v1 > id) edge.v1--;
      if (edge.v2 > id) edge.v2--;
    });

    // Update face references
    faces.forEach(face => {
      face.loop = face.loop.map(vertexId => {
        return vertexId > id ? vertexId - 1 : vertexId;
      }).filter(id => id < vertices.length);
    });
  }

  function clearAll() {
    // Clear arrays
    vertices = [];
    edges = [];
    faces = [];
    selectedVertices = [];

    // Remove objects from scene
    vertexObjects.forEach(obj => scene.remove(obj));
    edgeObjects.forEach(obj => scene.remove(obj));
    faceObjects.forEach(obj => scene.remove(obj));

    // Clear object arrays
    vertexObjects = [];
    edgeObjects = [];
    faceObjects = [];
    selectedEdgeIndex = -1;

    // Update UI
    updateVertexList();
    updateEdgeList();
    updateFaceOptions();
    document.getElementById('faceSelect').innerHTML = '<option value="">Select a closed loop...</option>';
  }

  // Handle window resize
  window.addEventListener('resize', () => {
    camera.aspect = window.innerWidth / (window.innerHeight * 0.6);
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight * 0.6);
  });

  // Initialize the application
  init();
</script>
</body>
</html>
